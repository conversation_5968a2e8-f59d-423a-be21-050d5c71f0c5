'use client'

import React, { useState, useEffect } from 'react'
import { safeToLocaleDateString } from '@/lib/date-utils'

interface ReplyModalProps {
  isOpen: boolean
  onClose: () => void
  contactForm: any
  onReplySuccess: () => void
}

export function ReplyModal({
  isOpen,
  onClose,
  contactForm,
  onReplySuccess
}: ReplyModalProps) {
  const [formData, setFormData] = useState({
    subject: '',
    message: '',
  })
  const [statusData, setStatusData] = useState({
    status: '',
    isRead: false,
  })
  const [forwardData, setForwardData] = useState({
    teamMember: '',
    forwardMessage: '',
  })
  const [teamMembers, setTeamMembers] = useState([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    if (contactForm) {
      setFormData({
        subject: `Re: ${contactForm.subject}`,
        message: '',
      })
      setStatusData({
        status: contactForm.status || 'New',
        isRead: true, // Mark as read when admin opens the reply form
      })

      // Mark the contact form as read when opening the reply modal
      handleMarkAsRead()
    }
  }, [contactForm])

  // Load team members
  useEffect(() => {
    const fetchTeamMembers = async () => {
      try {
        const response = await fetch('/api/admin/team-members?limit=100')
        if (response.ok) {
          const data = await response.json()
          setTeamMembers(data.data || [])
        }
      } catch (error) {
        console.error('Failed to fetch team members:', error)
      }
    }

    if (isOpen) {
      fetchTeamMembers()
    }
  }, [isOpen])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  // Mark contact form as read
  const handleMarkAsRead = async () => {
    if (!contactForm) return

    try {
      await fetch(`/api/admin/contact-forms/${contactForm.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isread: true }),
      })
    } catch (error) {
      console.error('Failed to mark as read:', error)
    }
  }



  const handleCallPhone = () => {
    if (contactForm.phone) {
      window.open(`tel:${contactForm.phone}`, '_self')
    }
  }

  const handleForwardMessage = async () => {
    if (!forwardData.teamMember) {
      setError('Please select a team member to forward to')
      return
    }

    try {
      const response = await fetch(`/api/admin/contact-forms/${contactForm.id}/forward`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          teamMember: forwardData.teamMember,
          message: forwardData.forwardMessage,
          adminReply: formData.message, // Include admin reply in forward
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to forward message')
      }

      // Reset forward form
      setForwardData({ teamMember: '', forwardMessage: '' })
      setError('')
      alert('Message forwarded successfully!')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to forward message'
      setError(errorMessage)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setError('')

    try {
      // Send reply
      const response = await fetch(`/api/admin/contact-forms/${contactForm.id}/reply`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to send reply')
      }

      onReplySuccess()
      onClose()
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to send reply'
      setError(errorMessage)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleStatusUpdate = async () => {
    try {
      const response = await fetch(`/api/admin/contact-forms/${contactForm.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          status: statusData.status,
          isRead: statusData.isRead,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update status')
      }

      onReplySuccess()
      setError('')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update status'
      setError(errorMessage)
    }
  }

  const handleClose = () => {
    if (!isSubmitting) {
      onClose()
    }
  }

  if (!isOpen || !contactForm) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 py-6">
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm" onClick={handleClose} />

        <div className="relative bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[85vh] overflow-hidden">
          {/* Simple Header */}
          <div className="bg-gray-50 px-4 py-3 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-lg font-semibold text-gray-900">Reply to Contact</h2>
                <p className="text-sm text-gray-600">From: {contactForm.name} • {contactForm.email}</p>
              </div>
              <button
                type="button"
                onClick={handleClose}
                disabled={isSubmitting}
                className="text-gray-400 hover:text-gray-600 transition-colors disabled:opacity-50"
              >
                <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          <div className="flex h-[calc(85vh-80px)]">
            {/* Left Panel - Original Message */}
            <div className="w-1/2 bg-gray-50 border-r border-gray-200 p-4 overflow-y-auto">
              <div className="flex flex-col h-full space-y-3">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-base font-semibold text-gray-900">Original Message</h3>
                  <span className="text-xs text-gray-500">{safeToLocaleDateString(contactForm.createdAt)}</span>
                </div>

                {/* Contact Info */}
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    <span className="text-sm font-medium text-gray-900">{contactForm.name}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    <span className="text-sm text-gray-700">{contactForm.email}</span>
                  </div>
                  {contactForm.phone && (
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                        </svg>
                        <span className="text-sm text-gray-700">{contactForm.phone}</span>
                      </div>
                      <button
                        type="button"
                        onClick={handleCallPhone}
                        className="flex items-center px-2 py-1 text-xs font-medium text-green-700 bg-green-50 border border-green-200 rounded hover:bg-green-100 focus:outline-none focus:ring-1 focus:ring-green-500"
                      >
                        <svg className="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                        </svg>
                        Call
                      </button>
                    </div>
                  )}
                </div>

                {/* Subject */}
                <div className="border-t border-gray-200 pt-2">
                  <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Subject</p>
                  <p className="text-sm text-gray-900 mt-1">{contactForm.subject}</p>
                </div>

                {/* Message */}
                <div className="border-t border-gray-200 pt-2 flex flex-col" style={{ height: '200px' }}>
                  <p className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2">Message</p>
                  <div className="bg-white rounded border border-gray-200 p-3 flex-1 overflow-y-auto">
                    <p className="text-sm text-gray-900 whitespace-pre-wrap leading-relaxed">{contactForm.message}</p>
                  </div>
                </div>

                {/* Status Management */}
                <div className="border-t border-gray-200 pt-3 mt-3">
                  <div className="flex items-center space-x-3 mb-2">
                    <div className="flex-1">
                      <label htmlFor="status" className="block text-xs font-medium text-gray-500 uppercase tracking-wide mb-1">
                        Status
                      </label>
                      <select
                        id="status"
                        value={statusData.status}
                        onChange={(e) => setStatusData(prev => ({ ...prev, status: e.target.value }))}
                        className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="New">New</option>
                        <option value="In Progress">In Progress</option>
                        <option value="Resolved">Resolved</option>
                        <option value="Closed">Closed</option>
                      </select>
                    </div>
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="isRead"
                        checked={statusData.isRead}
                        onChange={(e) => setStatusData(prev => ({ ...prev, isRead: e.target.checked }))}
                        className="h-3 w-3 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor="isRead" className="text-xs font-medium text-gray-700">
                        Mark as read
                      </label>
                    </div>
                    <button
                      type="button"
                      onClick={handleStatusUpdate}
                      className="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 focus:outline-none focus:ring-1 focus:ring-blue-500"
                    >
                      Update
                    </button>
                  </div>

                  {contactForm.readAt && (
                    <div className="text-xs text-gray-500 mt-1">
                      Read: {safeToLocaleDateString(contactForm.readAt)}
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Right Panel - Reply Form */}
            <div className="w-1/2 flex flex-col">
              {/* Compose Reply Section */}
              <div className="flex-1 p-4 flex flex-col">
                <h3 className="text-base font-semibold text-gray-900 mb-4">Compose Reply</h3>

                <form onSubmit={handleSubmit} className="flex flex-col flex-1 space-y-4">
                  {error && (
                    <div className="bg-red-50 border border-red-200 rounded p-3">
                      <div className="flex items-center">
                        <svg className="h-4 w-4 text-red-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                        </svg>
                        <p className="text-sm text-red-800">{error}</p>
                      </div>
                    </div>
                  )}

                  <div>
                    <label htmlFor="subject" className="block text-sm font-medium text-gray-700 mb-2">
                      Subject
                    </label>
                    <input
                      type="text"
                      id="subject"
                      name="subject"
                      value={formData.subject}
                      onChange={handleInputChange}
                      required
                      disabled={isSubmitting}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed"
                    />
                  </div>

                  <div className="flex flex-col" style={{ height: '200px' }}>
                    <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                      Reply Message
                    </label>
                    <div className="relative flex-1">
                      <textarea
                        id="message"
                        name="message"
                        value={formData.message}
                        onChange={handleInputChange}
                        required
                        disabled={isSubmitting}
                        placeholder="Type your reply message here..."
                        className="w-full h-full px-3 py-2 pr-12 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed resize-none"
                      />
                      <button
                        type="submit"
                        disabled={isSubmitting}
                        className="absolute bottom-2 right-2 p-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                        title={isSubmitting ? 'Sending...' : 'Send Reply'}
                      >
                        {isSubmitting ? (
                          <svg className="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                        ) : (
                          <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                          </svg>
                        )}
                      </button>
                    </div>
                  </div>
                </form>
              </div>

              {/* Quick Actions Section */}
              <div className="border-t border-gray-200 p-4 bg-gray-50">
                <div className="flex items-center space-x-2 mb-3">
                  <svg className="h-4 w-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                  </svg>
                  <h4 className="text-sm font-semibold text-gray-900">Forward to Team</h4>
                </div>
                <div className="space-y-3">
                  <div>
                    <label htmlFor="teamMember" className="block text-xs font-medium text-gray-700 mb-1">
                      Team Member
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                        <svg className="h-3 w-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                      </div>
                      <select
                        id="teamMember"
                        value={forwardData.teamMember}
                        onChange={(e) => setForwardData(prev => ({ ...prev, teamMember: e.target.value }))}
                        className="w-full pl-7 pr-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-purple-500 focus:border-purple-500"
                      >
                        <option value="">Select team member...</option>
                        {teamMembers.map((member: any) => (
                          <option key={member.id} value={member.email}>
                            {member.name} ({member.position})
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div>
                    <label htmlFor="forwardMessage" className="block text-xs font-medium text-gray-700 mb-1">
                      Note
                    </label>
                    <div className="relative">
                      <textarea
                        id="forwardMessage"
                        value={forwardData.forwardMessage}
                        onChange={(e) => setForwardData(prev => ({ ...prev, forwardMessage: e.target.value }))}
                        placeholder="Add a note..."
                        rows={2}
                        className="w-full px-2 py-1 pr-8 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-purple-500 focus:border-purple-500 resize-none"
                      />
                      <button
                        type="button"
                        onClick={handleForwardMessage}
                        className="absolute bottom-1 right-1 p-1 bg-purple-600 text-white rounded hover:bg-purple-700 focus:outline-none focus:ring-1 focus:ring-purple-500"
                        title="Forward Message"
                      >
                        <svg className="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}


