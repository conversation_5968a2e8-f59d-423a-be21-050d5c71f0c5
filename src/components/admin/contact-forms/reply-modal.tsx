'use client'

import React, { useState, useEffect } from 'react'
import { safeToLocaleDateString } from '@/lib/date-utils'

interface ReplyModalProps {
  isOpen: boolean
  onClose: () => void
  contactForm: any
  onReplySuccess: () => void
}

export function ReplyModal({
  isOpen,
  onClose,
  contactForm,
  onReplySuccess
}: ReplyModalProps) {
  const [formData, setFormData] = useState({
    subject: '',
    message: '',
    markAsResolved: false,
  })
  const [statusData, setStatusData] = useState({
    status: '',
    isRead: false,
  })
  const [forwardData, setForwardData] = useState({
    teamMember: '',
    forwardMessage: '',
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState('')

  useEffect(() => {
    if (contactForm) {
      setFormData({
        subject: `Re: ${contactForm.subject}`,
        message: '',
        markAsResolved: false,
      })
      setStatusData({
        status: contactForm.status || 'New',
        isRead: contactForm.isRead || false,
      })
    }
  }, [contactForm])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }))
  }

  const handleStatusChange = (e: React.ChangeEvent<HTMLSelectElement | HTMLInputElement>) => {
    const { name, value, type } = e.target
    const checked = type === 'checkbox' ? (e.target as HTMLInputElement).checked : undefined

    setStatusData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
  }

  const handleForwardChange = (e: React.ChangeEvent<HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setForwardData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleCallPhone = () => {
    if (contactForm.phone) {
      window.open(`tel:${contactForm.phone}`, '_self')
    }
  }

  const handleForwardMessage = async () => {
    if (!forwardData.teamMember) {
      setError('Please select a team member to forward to')
      return
    }

    try {
      const response = await fetch(`/api/admin/contact-forms/${contactForm.id}/forward`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          teamMember: forwardData.teamMember,
          message: forwardData.forwardMessage,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to forward message')
      }

      // Reset forward form
      setForwardData({ teamMember: '', forwardMessage: '' })
      setError('')
      // Show success message or close modal
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to forward message'
      setError(errorMessage)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setError('')

    try {
      // Send reply
      const response = await fetch(`/api/admin/contact-forms/${contactForm.id}/reply`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to send reply')
      }

      onReplySuccess()
      onClose()
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to send reply'
      setError(errorMessage)
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleStatusUpdate = async () => {
    try {
      const response = await fetch(`/api/admin/contact-forms/${contactForm.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          status: statusData.status,
          isRead: statusData.isRead,
        }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update status')
      }

      onReplySuccess()
      setError('')
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update status'
      setError(errorMessage)
    }
  }

  const handleClose = () => {
    if (!isSubmitting) {
      onClose()
    }
  }

  if (!isOpen || !contactForm) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 py-6">
        <div className="fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm" onClick={handleClose} />

        <div className="relative bg-white rounded-lg shadow-xl max-w-5xl w-full max-h-[90vh] overflow-hidden">
          {/* Compact Header */}
          <div className="bg-blue-600 px-4 py-3 border-b border-blue-700">
            <div className="flex items-center justify-between">
              <h2 className="text-lg font-semibold text-white">Reply to Contact Form</h2>
              <button
                type="button"
                onClick={handleClose}
                disabled={isSubmitting}
                className="text-white hover:text-blue-200 transition-colors disabled:opacity-50 p-1"
              >
                <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>

          <div className="flex max-h-[calc(90vh-60px)]">
            {/* Left Panel - Original Message */}
            <div className="w-2/5 bg-gray-50 border-r border-gray-200 p-4 overflow-y-auto">
              <div className="space-y-3">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-sm font-semibold text-gray-900">Original Message</h3>
                  <span className="text-xs text-gray-500">{safeToLocaleDateString(contactForm.createdAt)}</span>
                </div>

                {/* Compact Contact Info */}
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    <span className="text-sm font-medium text-gray-900">{contactForm.name}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    <span className="text-sm text-gray-700">{contactForm.email}</span>
                  </div>
                  {contactForm.phone && (
                    <div className="flex items-center space-x-2">
                      <svg className="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                      </svg>
                      <span className="text-sm text-gray-700">{contactForm.phone}</span>
                    </div>
                  )}
                </div>

                {/* Subject */}
                <div className="pt-2 border-t border-gray-200">
                  <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Subject</p>
                  <p className="text-sm text-gray-900 mt-1">{contactForm.subject}</p>
                </div>

                {/* Message */}
                <div className="pt-2 border-t border-gray-200">
                  <p className="text-xs font-medium text-gray-500 uppercase tracking-wide">Message</p>
                  <div className="bg-white rounded border border-gray-200 p-3 mt-1 max-h-32 overflow-y-auto">
                    <p className="text-sm text-gray-900 whitespace-pre-wrap leading-relaxed">{contactForm.message}</p>
                  </div>
                </div>

                {/* Status Management */}
                <div className="pt-3 border-t border-gray-200">
                  <p className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2">Status Management</p>
                  <div className="space-y-2">
                    <div>
                      <label htmlFor="status" className="block text-xs font-medium text-gray-600 mb-1">
                        Status
                      </label>
                      <select
                        id="status"
                        name="status"
                        value={statusData.status}
                        onChange={handleStatusChange}
                        className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="New">New</option>
                        <option value="In Progress">In Progress</option>
                        <option value="Resolved">Resolved</option>
                        <option value="Closed">Closed</option>
                      </select>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id="isRead"
                          name="isRead"
                          checked={statusData.isRead}
                          onChange={handleStatusChange}
                          className="h-3 w-3 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                        />
                        <label htmlFor="isRead" className="ml-1 text-xs text-gray-600">
                          Mark as read
                        </label>
                      </div>
                      {contactForm.readAt && (
                        <span className="text-xs text-gray-500">
                          Read: {safeToLocaleDateString(contactForm.readAt)}
                        </span>
                      )}
                    </div>

                    <button
                      type="button"
                      onClick={handleStatusUpdate}
                      className="w-full px-2 py-1 text-xs font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded hover:bg-blue-100 focus:outline-none focus:ring-1 focus:ring-blue-500"
                    >
                      Update Status
                    </button>
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="pt-3 border-t border-gray-200">
                  <p className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2">Quick Actions</p>
                  <div className="space-y-2">
                    {contactForm.phone && (
                      <button
                        type="button"
                        onClick={handleCallPhone}
                        className="w-full flex items-center justify-center px-2 py-1.5 text-xs font-medium text-green-700 bg-green-50 border border-green-200 rounded hover:bg-green-100 focus:outline-none focus:ring-1 focus:ring-green-500"
                      >
                        <svg className="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                        </svg>
                        Call {contactForm.phone}
                      </button>
                    )}

                    <div>
                      <label htmlFor="teamMember" className="block text-xs font-medium text-gray-600 mb-1">
                        Forward to Team Member
                      </label>
                      <select
                        id="teamMember"
                        name="teamMember"
                        value={forwardData.teamMember}
                        onChange={handleForwardChange}
                        className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="">Select team member...</option>
                        <option value="<EMAIL>">Admin Team</option>
                        <option value="<EMAIL>">Support Team</option>
                        <option value="<EMAIL>">Sales Team</option>
                        <option value="<EMAIL>">Technical Team</option>
                      </select>
                    </div>

                    {forwardData.teamMember && (
                      <div>
                        <textarea
                          name="forwardMessage"
                          value={forwardData.forwardMessage}
                          onChange={handleForwardChange}
                          placeholder="Add a note for the team member..."
                          rows={2}
                          className="w-full px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 resize-none"
                        />
                        <button
                          type="button"
                          onClick={handleForwardMessage}
                          className="w-full mt-1 px-2 py-1 text-xs font-medium text-purple-700 bg-purple-50 border border-purple-200 rounded hover:bg-purple-100 focus:outline-none focus:ring-1 focus:ring-purple-500"
                        >
                          Forward Message
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Right Panel - Reply Form */}
            <div className="flex-1 flex flex-col">
              <form onSubmit={handleSubmit} className="flex flex-col h-full">
                <div className="flex-1 p-4 space-y-4 overflow-y-auto">
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-semibold text-gray-900">Compose Reply</h3>
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        id="markAsResolved"
                        name="markAsResolved"
                        checked={formData.markAsResolved}
                        onChange={handleCheckboxChange}
                        disabled={isSubmitting}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:cursor-not-allowed"
                      />
                      <label htmlFor="markAsResolved" className="text-xs text-gray-600">
                        Mark as resolved
                      </label>
                    </div>
                  </div>

                  {error && (
                    <div className="bg-red-50 border border-red-200 rounded p-3">
                      <div className="flex items-center">
                        <svg className="h-4 w-4 text-red-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                        </svg>
                        <p className="text-xs text-red-800">{error}</p>
                      </div>
                    </div>
                  )}

                  <div>
                    <label htmlFor="subject" className="block text-xs font-medium text-gray-700 mb-1">
                      Subject
                    </label>
                    <input
                      type="text"
                      id="subject"
                      name="subject"
                      value={formData.subject}
                      onChange={handleInputChange}
                      required
                      disabled={isSubmitting}
                      className="w-full px-3 py-2 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed"
                    />
                  </div>

                  <div className="flex-1 flex flex-col">
                    <label htmlFor="message" className="block text-xs font-medium text-gray-700 mb-1">
                      Reply Message
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      required
                      disabled={isSubmitting}
                      placeholder="Type your reply message here..."
                      className="flex-1 w-full px-3 py-2 text-sm border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed resize-none min-h-[200px]"
                    />
                  </div>
                </div>

                {/* Compact Footer */}
                <div className="border-t border-gray-200 px-4 py-3 bg-gray-50">
                  <div className="flex justify-between items-center">
                    <div className="text-xs text-gray-500">
                      Reply will be sent to {contactForm.email}
                    </div>
                    <div className="flex space-x-2">
                      <button
                        type="button"
                        onClick={handleClose}
                        disabled={isSubmitting}
                        className="px-3 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded hover:bg-gray-50 focus:outline-none focus:ring-1 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        disabled={isSubmitting}
                        className="px-4 py-1.5 text-xs font-medium text-white bg-blue-600 border border-transparent rounded hover:bg-blue-700 focus:outline-none focus:ring-1 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isSubmitting ? (
                          <div className="flex items-center">
                            <svg className="animate-spin -ml-1 mr-1 h-3 w-3 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Sending...
                          </div>
                        ) : (
                          'Send Reply'
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
