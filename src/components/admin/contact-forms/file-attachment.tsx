'use client'

import React, { useState, useRef } from 'react'
import { CloudArrowUpIcon, XMarkIcon, DocumentIcon, PhotoIcon } from '@heroicons/react/24/outline'

export interface AttachmentFile {
  id: string
  filename: string
  size: number
  mimeType: string
  url: string
  path: string
}

interface FileAttachmentProps {
  attachments: AttachmentFile[]
  onAttachmentsChange: (attachments: AttachmentFile[]) => void
  disabled?: boolean
  maxFiles?: number
  className?: string
}

const FileAttachment = ({ 
  attachments, 
  onAttachmentsChange, 
  disabled = false,
  maxFiles = 5,
  className = ""
}: FileAttachmentProps) => {
  const [uploading, setUploading] = useState(false)
  const [dragOver, setDragOver] = useState(false)
  const [error, setError] = useState('')
  const fileInputRef = useRef<HTMLInputElement>(null)

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getFileIcon = (mimeType: string) => {
    if (mimeType.startsWith('image/')) {
      return <PhotoIcon className="w-4 h-4 text-blue-500 flex-shrink-0" />
    }
    return <DocumentIcon className="w-4 h-4 text-gray-500 flex-shrink-0" />
  }

  const handleFileSelect = async (files: FileList) => {
    if (disabled || uploading) return

    setError('')
    
    // Check file count limit
    if (attachments.length + files.length > maxFiles) {
      setError(`Maximum ${maxFiles} files allowed`)
      return
    }

    setUploading(true)

    try {
      const formData = new FormData()
      Array.from(files).forEach(file => {
        formData.append('files', file)
      })

      const response = await fetch('/api/admin/contact-forms/attachments', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Upload failed')
      }

      const result = await response.json()
      const newAttachments = result.data.attachments

      onAttachmentsChange([...attachments, ...newAttachments])
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Upload failed'
      setError(errorMessage)
    } finally {
      setUploading(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
    
    if (e.dataTransfer.files) {
      handleFileSelect(e.dataTransfer.files)
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
  }

  const removeAttachment = (id: string) => {
    onAttachmentsChange(attachments.filter(att => att.id !== id))
  }

  const openFileDialog = () => {
    if (!disabled && !uploading) {
      fileInputRef.current?.click()
    }
  }

  return (
    <div className={`space-y-2 ${className}`}>
      {/* Compact Upload Area */}
      <div
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={openFileDialog}
        className={`
          border-2 border-dashed rounded-md p-2 text-center cursor-pointer transition-colors
          ${dragOver
            ? 'border-blue-400 bg-blue-50'
            : 'border-gray-300 hover:border-gray-400'
          }
          ${disabled || uploading ? 'opacity-50 cursor-not-allowed' : ''}
          ${attachments.length >= maxFiles ? 'opacity-50 cursor-not-allowed' : ''}
        `}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept=".pdf,.doc,.docx,.xls,.xlsx,.txt,.jpg,.jpeg,.png,.gif,.webp"
          onChange={(e) => e.target.files && handleFileSelect(e.target.files)}
          className="hidden"
          disabled={disabled || uploading || attachments.length >= maxFiles}
        />

        <div className="flex items-center justify-center space-x-2">
          <CloudArrowUpIcon className="h-4 w-4 text-gray-400 flex-shrink-0" />
          <div className="text-left flex-1">
            <p className="text-xs text-gray-600">
              {uploading ? 'Uploading...' :
               attachments.length >= maxFiles ? `Max ${maxFiles} files` :
               dragOver ? 'Drop files here' : 'Click or drag to attach files'}
            </p>
            <p className="text-xs text-gray-500">
              PDF, DOC, Images • Max 10MB
            </p>
          </div>
          {uploading && (
            <div className="inline-block animate-spin rounded-full h-3 w-3 border-b-2 border-blue-600 flex-shrink-0"></div>
          )}
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-3">
          <p className="text-sm text-red-700">{error}</p>
        </div>
      )}

      {/* Compact Attached Files */}
      {attachments.length > 0 && (
        <div className="space-y-1">
          <h4 className="text-xs font-medium text-gray-700 flex items-center justify-between">
            <span>Attached Files ({attachments.length}/{maxFiles})</span>
          </h4>
          <div className="max-h-24 overflow-y-auto space-y-1">
            {attachments.map((attachment) => (
              <div
                key={attachment.id}
                className="flex items-center justify-between p-2 bg-gray-50 rounded border text-xs"
              >
                <div className="flex items-center space-x-2 flex-1 min-w-0">
                  {getFileIcon(attachment.mimeType)}
                  <div className="flex-1 min-w-0">
                    <p className="font-medium text-gray-900 truncate">
                      {attachment.filename}
                    </p>
                    <p className="text-gray-500">
                      {formatFileSize(attachment.size)}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-1 ml-2">
                  <a
                    href={attachment.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-600 hover:text-blue-800 px-1"
                    title="View file"
                  >
                    👁️
                  </a>
                  <button
                    type="button"
                    onClick={() => removeAttachment(attachment.id)}
                    disabled={disabled}
                    className="text-red-600 hover:text-red-800 disabled:opacity-50 px-1"
                    title="Remove attachment"
                  >
                    <XMarkIcon className="w-3 h-3" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default FileAttachment
