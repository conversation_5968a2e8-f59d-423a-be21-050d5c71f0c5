import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import {
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  successResponse,
  requireAdmin,
  validateRequest,
  ApiError
} from '@/lib/api-utils'
import { schemas } from '@/lib/validations'
import { transformToDbFields, transformFromDbFields } from '@/lib/data-transform'
import { z } from 'zod'

interface RouteParams {
  params: { id: string }
}

// GET /api/admin/contact-forms/[id] - Get a specific contact form
export const GET = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const resolvedParams = await params // Await params for Next.js 15

  const contactForm = await prisma.contactforms.findUnique({
    where: { id: BigInt(resolvedParams.id) },
  })

  if (!contactForm) {
    throw new ApiError('Contact form not found', 404)
  }

  // Transform BigInt to string for JSON serialization
  const transformedContactForm = {
    ...contactForm,
    id: contactForm.id.toString(),
    createdAt: contactForm.createdat?.toISOString(),
    updatedAt: contactForm.updatedat?.toISOString(),
  }

  return Response.json({
    success: true,
    data: transformedContactForm
  })
})

// PUT /api/admin/contact-forms/[id] - Update a contact form
export const PUT = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const body = await request.json()

  // Validate the data
  const validate = validateRequest(schemas.contactForm.update)
  const data = await validate(request)

  // Prepare update data
  const updateData: any = {}
  if (data.name !== undefined) updateData.name = data.name
  if (data.email !== undefined) updateData.email = data.email
  if (data.phone !== undefined) updateData.phone = data.phone
  if (data.subject !== undefined) updateData.subject = data.subject
  if (data.message !== undefined) updateData.message = data.message
  if (data.status !== undefined) updateData.status = data.status
  if (data.isread !== undefined) {
    updateData.isread = data.isread
    updateData.readat = data.isread ? new Date() : null
  }
  updateData.updatedat = new Date()

  const contactForm = await prisma.contactforms.update({
    where: { id: BigInt(params.id) },
    data: updateData,
  })

  // Transform BigInt to string for JSON serialization
  const transformedContactForm = {
    ...contactForm,
    id: contactForm.id.toString(),
    createdAt: contactForm.createdat?.toISOString(),
    updatedAt: contactForm.updatedat?.toISOString(),
  }

  return Response.json({
    success: true,
    data: transformedContactForm,
    message: 'Contact form updated successfully'
  })
})

// PATCH /api/admin/contact-forms/[id] - Partial update (for status changes)
export const PATCH = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const body = await request.json()
  const resolvedParams = await params // Await params for Next.js 15

  // Prepare update data
  const updateData: any = { updatedat: new Date() }

  if (body.isread !== undefined) {
    updateData.isread = body.isread
    updateData.readat = body.isread ? new Date() : null
  }
  if (body.status !== undefined) updateData.status = body.status

  const contactForm = await prisma.contactforms.update({
    where: { id: BigInt(resolvedParams.id) },
    data: updateData,
  })

  // Transform BigInt to string for JSON serialization
  const transformedContactForm = {
    ...contactForm,
    id: contactForm.id.toString(),
    createdAt: contactForm.createdat?.toISOString(),
    updatedAt: contactForm.updatedat?.toISOString(),
  }

  return Response.json({
    success: true,
    data: transformedContactForm,
    message: 'Contact form updated successfully'
  })
})

// DELETE /api/admin/contact-forms/[id] - Delete a contact form
export const DELETE = withErrorHandler(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  await prisma.contactforms.delete({
    where: { id: BigInt(params.id) }
  })

  return Response.json({
    success: true,
    message: 'Contact form deleted successfully'
  })
})
