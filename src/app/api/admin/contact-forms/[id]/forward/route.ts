import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import {
  with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  successResponse,
  requireAdmin,
  validateRequest,
  ApiError
} from '@/lib/api-utils'
import { z } from 'zod'
import nodemailer from 'nodemailer'

interface RouteParams {
  params: { id: string }
}

// Validation schema for forward request
const forwardSchema = z.object({
  teamMember: z.string().min(1, 'Team member is required'),
  message: z.string().min(1, 'Forward message is required'),
  adminReply: z.string().optional(),
})

// POST /api/admin/contact-forms/[id]/forward - Forward a contact form to team member
export const POST = withE<PERSON>rH<PERSON><PERSON>(async (request: NextRequest, { params }: RouteParams) => {
  await requireAdmin(request)

  const body = await request.json()

  // Validate the request data
  const { teamMember, message, adminReply } = forwardSchema.parse(body)

  // Get the contact form
  const contactForm = await prisma.contactforms.findUnique({
    where: { id: BigInt(params.id) },
  })

  if (!contactForm) {
    throw new ApiError('Contact form not found', 404)
  }

  // Create email transporter
  const transporter = nodemailer.createTransporter({
    host: process.env.SMTP_HOST || 'smtp.gmail.com',
    port: parseInt(process.env.SMTP_PORT || '587'),
    secure: false,
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASS,
    },
  })

  // Prepare email content
  const emailSubject = `Forwarded Contact Form: ${contactForm.subject}`
  const emailHtml = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px;">
        Forwarded Contact Form
      </h2>
      
      <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3 style="color: #007bff; margin-top: 0;">Forward Message:</h3>
        <p style="font-style: italic; color: #666;">${message}</p>
      </div>

      ${adminReply ? `
      <div style="background-color: #e8f5e8; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #28a745;">
        <h3 style="color: #28a745; margin-top: 0;">Admin Reply:</h3>
        <p style="color: #333;">${adminReply.replace(/\n/g, '<br>')}</p>
      </div>
      ` : ''}

      <div style="background-color: #fff; border: 1px solid #ddd; border-radius: 8px; padding: 20px; margin: 20px 0;">
        <h3 style="color: #333; margin-top: 0;">Original Contact Form Details:</h3>
        
        <table style="width: 100%; border-collapse: collapse;">
          <tr>
            <td style="padding: 8px 0; font-weight: bold; color: #555; width: 120px;">Name:</td>
            <td style="padding: 8px 0; color: #333;">${contactForm.name}</td>
          </tr>
          <tr>
            <td style="padding: 8px 0; font-weight: bold; color: #555;">Email:</td>
            <td style="padding: 8px 0; color: #333;">${contactForm.email}</td>
          </tr>
          ${contactForm.phone ? `
          <tr>
            <td style="padding: 8px 0; font-weight: bold; color: #555;">Phone:</td>
            <td style="padding: 8px 0; color: #333;">${contactForm.phone}</td>
          </tr>
          ` : ''}
          <tr>
            <td style="padding: 8px 0; font-weight: bold; color: #555;">Subject:</td>
            <td style="padding: 8px 0; color: #333;">${contactForm.subject}</td>
          </tr>
          <tr>
            <td style="padding: 8px 0; font-weight: bold; color: #555;">Status:</td>
            <td style="padding: 8px 0; color: #333;">${contactForm.status}</td>
          </tr>
          <tr>
            <td style="padding: 8px 0; font-weight: bold; color: #555;">Submitted:</td>
            <td style="padding: 8px 0; color: #333;">${contactForm.createdat?.toLocaleDateString()}</td>
          </tr>
        </table>

        <div style="margin-top: 20px;">
          <h4 style="color: #555; margin-bottom: 10px;">Message:</h4>
          <div style="background-color: #f8f9fa; padding: 15px; border-radius: 4px; border-left: 4px solid #007bff;">
            ${contactForm.message.replace(/\n/g, '<br>')}
          </div>
        </div>
      </div>

      <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">
        <p style="color: #666; font-size: 14px;">
          This contact form was forwarded from the admin panel.
        </p>
      </div>
    </div>
  `

  // Send email to team member
  await transporter.sendMail({
    from: process.env.SMTP_FROM || process.env.SMTP_USER,
    to: teamMember,
    subject: emailSubject,
    html: emailHtml,
  })

  // Update contact form to mark as forwarded (optional)
  await prisma.contactforms.update({
    where: { id: BigInt(params.id) },
    data: {
      updatedat: new Date(),
      // You could add a forwarded field to track this
    },
  })

  return Response.json({
    success: true,
    message: `Contact form forwarded to ${teamMember} successfully`
  })
})
