{"name": "technoloway-simple", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "dev:turbo": "next dev --turbo", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "type-check": "tsc --noEmit", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "db:setup": "node scripts/setup-database.js", "db:reset": "prisma migrate reset --force && npm run db:seed", "analyze": "cross-env ANALYZE=true next build", "postbuild": "next-sitemap"}, "dependencies": {"@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.1.1", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.9.0", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "@tiptap/extension-color": "^2.23.0", "@tiptap/extension-list-item": "^2.23.0", "@tiptap/extension-text-align": "^2.23.0", "@tiptap/extension-text-style": "^2.23.0", "@tiptap/extension-underline": "^2.23.0", "@tiptap/react": "^2.23.0", "@tiptap/starter-kit": "^2.23.0", "@types/bcryptjs": "^2.4.6", "@types/multer": "^1.4.12", "@types/nodemailer": "^6.4.14", "@types/uuid": "^10.0.0", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "clsx": "^2.1.1", "dotenv": "^16.4.7", "framer-motion": "^12.19.1", "jose": "^5.9.6", "lightningcss": "^1.30.1", "lucide-react": "^0.514.0", "multer": "^1.4.5-lts.1", "next": "15.3.3", "next-auth": "^4.24.10", "next-themes": "^0.4.6", "node-fetch": "^3.3.2", "nodemailer": "^6.9.15", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.2.9", "react-hook-form": "^7.58.1", "react-hot-toast": "^2.5.2", "sharp": "^0.33.5", "stripe": "^18.2.1", "swiper": "^11.2.8", "tailwind-merge": "^3.3.1", "uuid": "^10.0.0", "zod": "^3.25.67"}, "devDependencies": {"@eslint/eslintrc": "^3", "@faker-js/faker": "^9.8.0", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.4.8", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.13", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "15.3.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "next-sitemap": "^4.2.3", "node-mocks-http": "^1.15.1", "prisma": "^6.1.0", "tailwindcss": "^4", "tsx": "^4.19.2", "typescript": "^5"}}