// Simple test script for contact form API
const BASE_URL = 'http://localhost:3001'

async function testContactFormSubmission() {
  console.log('Testing contact form submission...')
  
  const testData = {
    name: 'Test User',
    email: '<EMAIL>',
    phone: '+****************',
    subject: 'Test Contact Form Submission',
    message: 'This is a test message to verify the contact form functionality.'
  }
  
  try {
    const response = await fetch(`${BASE_URL}/api/contact`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData)
    })
    
    const result = await response.json()
    
    console.log('Response status:', response.status)
    console.log('Response headers:')
    for (const [key, value] of response.headers.entries()) {
      if (key.startsWith('x-') || key.includes('rate') || key.includes('security')) {
        console.log(`  ${key}: ${value}`)
      }
    }
    console.log('Response body:', result)
    
    if (response.ok) {
      console.log('✅ Contact form submission successful!')
      return result.data
    } else {
      console.log('❌ Contact form submission failed:', result.error)
      return null
    }
  } catch (error) {
    console.error('❌ Error testing contact form:', error.message)
    return null
  }
}

async function testRateLimit() {
  console.log('\nTesting rate limiting...')
  
  const testData = {
    name: 'Rate Limit Test',
    email: '<EMAIL>',
    phone: '+****************',
    subject: 'Rate Limit Test',
    message: 'Testing rate limiting functionality.'
  }
  
  // Try to submit multiple times quickly
  for (let i = 1; i <= 5; i++) {
    console.log(`Attempt ${i}:`)
    
    try {
      const response = await fetch(`${BASE_URL}/api/contact`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...testData,
          subject: `Rate Limit Test ${i}`
        })
      })
      
      const result = await response.json()
      
      if (response.status === 429) {
        console.log(`  ✅ Rate limit triggered (${response.status})`)
        console.log(`  Retry-After: ${response.headers.get('retry-after')} seconds`)
        console.log(`  Rate limit remaining: ${response.headers.get('x-ratelimit-remaining')}`)
        break
      } else if (response.ok) {
        console.log(`  ✅ Submission ${i} successful`)
      } else {
        console.log(`  ❌ Submission ${i} failed:`, result.error)
      }
    } catch (error) {
      console.error(`  ❌ Error in attempt ${i}:`, error.message)
    }
    
    // Small delay between attempts
    await new Promise(resolve => setTimeout(resolve, 100))
  }
}

async function testSecurityValidation() {
  console.log('\nTesting security validation...')
  
  const maliciousData = {
    name: '<script>alert("xss")</script>',
    email: '<EMAIL>',
    subject: 'SELECT * FROM users',
    message: 'javascript:alert("xss")'
  }
  
  try {
    const response = await fetch(`${BASE_URL}/api/contact`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(maliciousData)
    })
    
    const result = await response.json()
    
    if (response.status === 400 && result.error.includes('Invalid form submission')) {
      console.log('✅ Security validation working - malicious content blocked')
    } else {
      console.log('❌ Security validation failed - malicious content not blocked')
    }
  } catch (error) {
    console.error('❌ Error testing security validation:', error.message)
  }
}

async function testAdminContactFormsAPI() {
  console.log('\nTesting admin contact forms API...')
  
  try {
    const response = await fetch(`${BASE_URL}/api/admin/contact-forms`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    })
    
    if (response.status === 401) {
      console.log('✅ Admin API properly protected (401 Unauthorized)')
    } else {
      console.log('❌ Admin API not properly protected')
    }
  } catch (error) {
    console.error('❌ Error testing admin API:', error.message)
  }
}

// Run all tests
async function runTests() {
  console.log('🧪 Starting Contact Form System Tests\n')
  
  await testContactFormSubmission()
  await testRateLimit()
  await testSecurityValidation()
  await testAdminContactFormsAPI()
  
  console.log('\n🏁 Tests completed!')
}

runTests().catch(console.error)
